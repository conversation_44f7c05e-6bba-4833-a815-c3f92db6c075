<template>
    <div class="p-6 space-y-6 bg-white shadow-sm">
        <!-- Loading State -->
        <div v-if="loading" class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-secondary"></div>
            <span class="ml-2 text-gray-600">Loading outlier data...</span>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex items-center">
                <span class="text-red-800 font-medium">Error loading Outlier data</span>
            </div>
        </div>

        <!-- Main Content -->
        <div v-else>
            <!-- Header Filters -->
            <div class="filter-container">
                <div class="filter-item">
                    <span class="filter-label">SubClass:</span>
                    <Multiselect
                        v-model="filters.subclass"
                        :options="subclassOptionsWithAll"
                        :placeholder="`${subclassOptionsWithAll.length - 1} Subclasses Available`"
                        :allow-empty="true"
                        mode="multiple"
                        :searchable="true"
                        class="text-sm"
                        @update:model-value="handleSubclassChange"
                        @open="dropdownStates.subclass = true"
                        @close="dropdownStates.subclass = false"
                        :close-on-select="false"
                        :open-direction="'bottom'"
                        :is-open="false"
                    />
                </div>
                <div class="filter-item">
                    <span class="filter-label">Store:</span>
                    <Multiselect
                        v-model="filters.storeId"
                        :options="storeOptionsWithAll"
                        :placeholder="`${storeOptionsWithAll.length - 1} Stores Available`"
                        :allow-empty="true"
                        mode="multiple"
                        :searchable="true"
                        class="text-sm"
                        @update:model-value="handleStoreChange"
                        @open="dropdownStates.store = true"
                        @close="dropdownStates.store = false"
                        :close-on-select="false"
                        :open-direction="'bottom'"
                        :is-open="false"
                    />
                </div>
                <div class="filter-item">
                    <span class="filter-label">Outlier Type:</span>
                    <Multiselect
                        v-model="filters.outlierStatus"
                        :options="outlierStatusOptionsWithLabels"
                        placeholder="All Outlier Types"
                        :allow-empty="true"
                        mode="multiple"
                        :searchable="true"
                        class="text-sm"
                        @update:model-value="handleOutlierChange"
                        @open="dropdownStates.outlier = true"
                        @close="dropdownStates.outlier = false"
                        :close-on-select="false"
                        :open-direction="'bottom'"
                        :is-open="false"
                    />
                </div>
                <div class="button-container">
                    <button 
                        @click="applyFilters"
                        class="px-4 py-2 bg-secondary text-white rounded hover:bg-secondary/80 text-sm font-medium transition-colors"
                    >
                        Apply Filters
                    </button>
                    <button 
                        @click="resetFilters"
                        class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 text-sm font-medium transition-colors"
                    >
                        Reset Filters
                    </button>
                </div>
            </div>

            <!-- Data Table -->
            <div class="overflow-x-auto">
                <table class="w-full text-sm border border-gray-200 rounded-lg">
                    <thead class="bg-primary/50">
                        <tr class="text-left text-tertiary font-semibold">
                            <th class="p-3">Subclass</th>
                            <th class="p-3">Store ID</th>
                            <th class="p-3">Month</th>
                            <th class="p-3">LM</th>
                            <th class="p-3">LM Contribution</th>
                            <th class="p-3">GMV/day</th>
                            <th class="p-3">Outlier Status</th>
                            <th class="p-3">Suggested Value</th>
                            <th class="p-3">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(row, i) in paginatedRows" :key="i"
                            :class="row.outlierStatus === 'Major Outlier' ? 'bg-white' : 'bg-white'">
                            <td class="p-3">{{ row.subclass }}</td>
                            <td class="p-3">{{ row.storeId }}</td>
                            <td class="p-3">{{ row.month }}</td>
                            <td class="p-3">{{ row.totalLm }}</td>
                            <td class="p-3">{{ row.lmContrib }}</td>
                            <td class="p-3">{{ row.gmvPerDay }}</td>
                            <td class="p-3">
                                <span
                                    :class="getOutlierStatusClass(row.outlierStatus)">
                                    {{ row.outlierStatus }}
                                </span>
                            </td>
                            <td class="p-3">{{ row.suggestedTotalLm }}</td>
                            <td class="p-3">
                                <!-- Show dropdown only for MAJOR_OUTLIER or MINOR_OUTLIER -->
                                <select v-if="isOutlier(row.outlierStatus)" v-model="row.action"
                                    class="border rounded px-2 py-1 bg-white cursor-pointer">
                                    <option value="Approve">Approve</option>
                                    <option value="Drop">Drop</option>
                                </select>
                                <span v-else class="text-gray-400 text-xs">-</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div v-if="rows.length > 0" class="flex items-center justify-between mt-4">
                <div class="text-sm text-gray-600">
                    Showing {{ startIndex + 1 }} to {{ endIndex }} of {{ rows.length }} results
                </div>
                <div class="flex items-center gap-2">
                    <button 
                        @click="currentPage = Math.max(1, currentPage - 1)"
                        :disabled="currentPage === 1"
                        class="px-3 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        Previous
                    </button>
                    
                    <div class="flex items-center gap-1">
                        <button 
                            v-for="page in visiblePages" 
                            :key="page"
                            @click="currentPage = page"
                            :class="[
                                'px-3 py-1 text-sm border rounded',
                                currentPage === page 
                                    ? 'bg-secondary text-white border-secondary' 
                                    : 'hover:bg-gray-50'
                            ]">
                            {{ page }}
                        </button>
                    </div>
                    
                    <button 
                        @click="currentPage = Math.min(totalPages, currentPage + 1)"
                        :disabled="currentPage === totalPages"
                        class="px-3 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        Next
                    </button>
                </div>
            </div>


            <!-- No Data State -->
            <div v-if="rows.length === 0 && !loading" class="text-center py-8 text-gray-500">
                No outlier data found matching the current filters.
            </div>
        </div>
    </div>
    <div v-if="showChartModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div class="bg-white rounded-xl shadow-lg w-[60vw] h-[45vw] p-3">
            <outlierChat @close="showChartModal = false" />
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import Multiselect from '@vueform/multiselect'
import outlierChat from './outlierChat.vue';
import axios from 'axios'

// Reactive state
const loading = ref(false)
const error = ref(null)
const allRows = ref([]) // Store all data
const rows = ref([]) // Store filtered data
const showChartModal = ref(false)
const currentPage = ref(1)
const itemsPerPage = 10

// Filters with initial defaults
const filters = ref({
    subclass: [], // Will be set to "All" after options are loaded
    storeId: [], // Will be set to "All" after options are loaded
    outlierStatus: ['MAJOR_OUTLIER', 'MINOR_OUTLIER'] // Major and Minor selected by default
})

// Track dropdown open states
const dropdownStates = ref({
    subclass: false,
    store: false,
    outlier: false
})

// Outlier status mapping (backend values to display labels)
const outlierStatusMapping = {
    'MAJOR_OUTLIER': 'Major',
    'MINOR_OUTLIER': 'Minor', 
    'NORMAL': 'Normal'
}

// Computed properties for dynamic options with "All" option
const subclassOptionsWithAll = computed(() => {
    const subclasses = [...new Set(allRows.value.map(row => row.subclass))]
    const sortedSubclasses = subclasses.sort()
    
    return ['All', ...sortedSubclasses]
})

// Store options dynamically filtered based on selected subclasses
const storeOptionsWithAll = computed(() => {
    let filtered = allRows.value
    
    // Filter by selected subclasses first (exclude "ALL")
    const selectedSubclasses = filters.value.subclass.filter(s => s !== 'ALL')
    if (selectedSubclasses.length > 0) {
        filtered = filtered.filter(row => selectedSubclasses.includes(row.subclass))
    }
    
    const stores = [...new Set(filtered.map(row => row.storeId))]
    const sortedStores = stores.sort()
    
    return ['All', ...sortedStores]
})

// Outlier status options with display labels
const outlierStatusOptionsWithLabels = computed(() => {
    return ['All', 'Major', 'Minor', 'Normal']
})

// Handler functions for filter changes
const handleSubclassChange = (newValue) => {
    // Handle "All" selection logic
    if (newValue.includes('All')) {
        // If "All" is selected, clear individual selections
        filters.value.subclass = ['All']
    } else if (newValue.length === 0) {
        // If nothing selected, treat as "All" selected
        filters.value.subclass = ['All']
    } else {
        // Remove "All" if individual items are selected
        filters.value.subclass = newValue.filter(v => v !== 'All')
        
        // If all individual items are selected, add "All"
        const allSubclasses = allRows.value.map(row => row.subclass)
        const uniqueSubclasses = [...new Set(allSubclasses)]
        if (newValue.length === uniqueSubclasses.length) {
            filters.value.subclass = ['All']
        }
    }
    
    // Clear store selection when subclass changes
    filters.value.storeId = ['All']
}

const handleStoreChange = (newValue) => {
    // Handle "All" selection logic
    if (newValue.includes('All')) {
        filters.value.storeId = ['All']
    } else if (newValue.length === 0) {
        filters.value.storeId = ['All']
    } else {
        filters.value.storeId = newValue.filter(v => v !== 'All')
        
        // If all available stores are selected, add "All"
        const availableStores = storeOptionsWithAll.value.filter(option => option !== 'All')
        
        if (newValue.length === availableStores.length) {
            filters.value.storeId = ['All']
        }
    }
}

const handleOutlierChange = (newValue) => {
    // Handle "All" selection logic
    if (newValue.includes('All')) {
        filters.value.outlierStatus = ['All']
    } else if (newValue.length === 0) {
        filters.value.outlierStatus = ['All']
    } else {
        // Convert display labels back to backend values
        const labelToValue = {
            'Major': 'MAJOR_OUTLIER',
            'Minor': 'MINOR_OUTLIER',
            'Normal': 'NORMAL'
        }
        
        filters.value.outlierStatus = newValue
            .filter(v => v !== 'All')
            .map(label => labelToValue[label] || label)
        
        // If all outlier types are selected, add "All"
        const allOutlierTypes = ['Major', 'Minor', 'Normal']
        if (newValue.length === allOutlierTypes.length) {
            filters.value.outlierStatus = ['All']
        }
    }
}

// API call function
const fetchOutlierData = async () => {
    loading.value = true
    error.value = null
    
    try {
        const response = await axios.get('scenario/outlier/')
        console.log("response.data", response.data)
        
        // Transform API response to match component structure
        allRows.value = response.data.map(item => ({
            subclass: item.sub_clss_nm,
            storeId: item.loc_cd,
            month: item.month,
            totalLm: item.total_lm,
            lmContrib: `${item.lm_contribution_in_store}%`,
            gmvPerDay: item.gmv_per_day,
            outlierStatus: item.outlier_status || 'NORMAL',
            suggestedTotalLm: item.suggested_total_lm,
            action: 'Approve' // Default action
        }))
        
        // Set initial "All" selections
        filters.value.subclass = ['All']
        filters.value.storeId = ['All']

        // Ensure dropdowns start closed - force close state
        dropdownStates.value = {
            subclass: false,
            store: false,
            outlier: false
        }

        // Force close any open dropdowns after a short delay
        setTimeout(() => {
            dropdownStates.value = {
                subclass: false,
                store: false,
                outlier: false
            }
        }, 100)
        
        // Apply initial filtering
        applyFilters()
        
    } catch (err) {
        console.error('Error fetching outlier data:', err)
        error.value = err.response?.data?.message || err.message || 'Failed to load outlier data'
    } finally {
        loading.value = false
    }
}

// Apply filters function
const applyFilters = () => {
    console.log('Applying filters:', filters.value)
    
    // Extract selected values (excluding "All")
    const selectedSubclasses = filters.value.subclass.filter(s => s !== 'All')
    const selectedStores = filters.value.storeId.filter(s => s !== 'All')
    const selectedOutliers = filters.value.outlierStatus.filter(s => s !== 'All')
    
    rows.value = allRows.value.filter(row => {
        // Subclass filter - "All" or empty = show all
        const matchSubclass = selectedSubclasses.length === 0 || 
                             selectedSubclasses.includes(row.subclass)
        
        // Store filter - "All" or empty = show all  
        const matchStore = selectedStores.length === 0 || 
                          selectedStores.includes(row.storeId)
        
        // Outlier status filter - "All" or empty = show all
        const matchOutlier = selectedOutliers.length === 0 || 
                            selectedOutliers.includes(row.outlierStatus)
        
        // All filters use AND logic
        return matchSubclass && matchStore && matchOutlier
    })
    
    // Reset to first page when filters change
    currentPage.value = 1
    
    // Log filter params for backend reference
    console.log('Filter params for backend:', {
        subclasses: selectedSubclasses,
        stores: selectedStores,
        outlierTypes: selectedOutliers
    })
}

// Reset filters function
const resetFilters = () => {
    filters.value = {
        subclass: ['All'], // "All" selected by default
        storeId: ['All'], // "All" selected by default
        outlierStatus: ['MAJOR_OUTLIER', 'MINOR_OUTLIER'] // back to default
    }
    applyFilters()
}

// Helper functions
const isOutlier = (status) => ["MAJOR_OUTLIER", "MINOR_OUTLIER"].includes(status);

const getOutlierStatusClass = (status) => {
    switch (status) {
        case "NORMAL":
            return "bg-positive text-white px-2 py-1 rounded text-xs";
        case "MINOR_OUTLIER":
            return "bg-neutral text-white px-2 py-1 rounded text-xs";
        case "MAJOR_OUTLIER":
            return "bg-negative text-white px-2 py-1 rounded text-xs";
        default:
            return "bg-gray-500 text-white px-2 py-1 rounded text-xs";
    }
};

// Computed properties for filtering and counting
const outlierCount = computed(() =>
    rows.value.filter(item => {
        const status = item.outlierStatus?.toLowerCase() || 'normal'
        return status.includes('outlier')
    }).length
)

const uniqueSubclassCount = computed(() => {
    const subclasses = new Set(
        rows.value
            .filter(item => {
                const status = item.outlierStatus?.toLowerCase() || 'normal'
                return status.includes('outlier')
            })
            .map(item => item.subclass)
    )
    return subclasses.size
})

// Pagination computed properties
const totalPages = computed(() => Math.ceil(rows.value.length / itemsPerPage))

const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage)

const endIndex = computed(() => Math.min(startIndex.value + itemsPerPage, rows.value.length))

const paginatedRows = computed(() => {
    return rows.value.slice(startIndex.value, endIndex.value)
})

const visiblePages = computed(() => {
    const total = totalPages.value
    const current = currentPage.value
    const delta = 2 // Show 2 pages before and after current page
    
    let start = Math.max(1, current - delta)
    let end = Math.min(total, current + delta)
    
    // Adjust if we're near the edges
    if (end - start < 4) {
        if (start === 1) {
            end = Math.min(total, start + 4)
        } else {
            start = Math.max(1, end - 4)
        }
    }
    
    const pages = []
    for (let i = start; i <= end; i++) {
        pages.push(i)
    }
    return pages
})

// Watch for filter changes to update store options
watch(() => filters.value.subclass, () => {
    // Clear store selection when subclass changes to prevent invalid selections
    filters.value.storeId = ['All']
}, { deep: true })

// Fetch data on component mount
onMounted(() => {
    fetchOutlierData()
})
</script>

<style scoped>
@import '@vueform/multiselect/themes/default.css';

/* Custom styling for multiselect components */
:deep(.multiselect) {
  min-height: 40px;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: white;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  width: 100%;
  min-width: 180px;
  position: relative;
}

/* Force dropdown to be hidden by default - override any conflicting styles */
:deep(.multiselect:not(.is-open) .multiselect-dropdown) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

:deep(.multiselect-dropdown) {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 50;
  max-height: 200px;
  overflow-y: auto;
  width: 100%;
  display: none; /* Hidden by default */
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
}

:deep(.multiselect-option) {
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  font-size: 0.875rem;
  transition: background-color 0.15s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

:deep(.multiselect-option:hover) {
  background-color: #f3f4f6;
}

:deep(.multiselect-option.is-selected) {
  background-color: #3b82f6;
  color: white;
}

/* Add checkboxes for multiselect options */
:deep(.multiselect-option::before) {
  content: '';
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  background-color: white;
  flex-shrink: 0;
  transition: all 0.15s ease;
  position: relative;
  display: inline-block;
}

:deep(.multiselect-option.is-selected::before) {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

:deep(.multiselect-option.is-selected::after) {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
}

/* Fix for "All" option styling */
:deep(.multiselect-option:first-child::before) {
  border-color: #3b82f6;
  background-color: #3b82f6;
}

:deep(.multiselect-option:first-child::after) {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
}

:deep(.multiselect-tag) {
  background-color: #3b82f6;
  color: white;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  margin: 0.125rem;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

:deep(.multiselect-tag-remove) {
  color: white;
  margin-left: 0.25rem;
  cursor: pointer;
  font-weight: bold;
}

:deep(.multiselect-placeholder) {
  color: #6b7280;
  font-size: 0.875rem;
}

:deep(.multiselect-search) {
  border: none;
  outline: none;
  padding: 0.5rem;
  width: 100%;
  font-size: 0.875rem;
  background: transparent;
}

:deep(.multiselect-clear) {
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
}

:deep(.multiselect-clear:hover) {
  color: #374151;
}

:deep(.multiselect-wrapper) {
  position: relative;
}

/* Fix for "No results found" message */
:deep(.multiselect-no-results) {
  padding: 0.5rem 0.75rem;
  color: #6b7280;
  font-size: 0.875rem;
  font-style: italic;
  position: relative;
  z-index: 1;
}

/* Fix overlay issues and ensure dropdowns are closed by default */
:deep(.multiselect) {
  position: relative;
}

/* Prevent default open state */
:deep(.multiselect.is-open) {
  z-index: 1001;
}

/* Ensure dropdown is hidden by default */
:deep(.multiselect-dropdown) {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #d1d5db;
  border-top: none;
  border-radius: 0 0 0.375rem 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  display: none; /* Hide by default */
}

/* Only show dropdown when multiselect is open */
:deep(.multiselect.is-open .multiselect-dropdown) {
  display: block;
}

/* Better positioning for no results message */
:deep(.multiselect-no-results) {
  padding: 0.5rem 0.75rem;
  color: #6b7280;
  font-size: 0.875rem;
  font-style: italic;
  background: white;
  border: 1px solid #d1d5db;
  border-top: none;
  border-radius: 0 0 0.375rem 0.375rem;
  display: none; /* Hide by default */
}

/* Only show no results when dropdown is open and no results */
:deep(.multiselect.is-open .multiselect-no-results) {
  display: block;
}

/* Ensure proper spacing from table */
.filter-container {
  margin-bottom: 2rem;
  position: relative;
  z-index: 10;
}

/* Better spacing for filter container */
.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 220px;
  flex: 1;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
  margin-bottom: 0.25rem;
}

/* Button container styling */
.button-container {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
  margin-top: 1.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .filter-item {
    min-width: 100%;
  }
  
  .button-container {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
