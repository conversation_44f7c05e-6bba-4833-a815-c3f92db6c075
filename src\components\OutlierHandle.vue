<template>
  <div>
    <div v-if="loading">Loading outlier data...</div>
    <div v-else-if="error" class="text-red-500">{{ error }}</div>
    <div v-else>
      <!-- Filters -->
           <div class="p-6 space-y-6 bg-white shadow-sm">
        <div class="flex flex-wrap items-center gap-4 mb-4">
            <div class="flex w-1/5 flex-col gap-1">
                <span class="text-sm font-semibold">SubClass:</span>
                <Multiselect v-model="filters.subclass" :options="subclassOptions" placeholder="All" :allow-empty="true"
                    class="text-sm" />
            </div>
            <div class="flex w-1/5 flex-col gap-1">
                <span class="text-sm font-semibold">Store:</span>
                <Multiselect v-model="filters.storeId" :options="storeOptions" placeholder="All" :allow-empty="true"
                    class="text-sm" />
            </div>
            <div class="flex w-1/5 flex-col gap-1">
                <span class="text-sm font-semibold">Outlier Type:</span>
                <Multiselect v-model="filters.outlierStatus" :options="outlierStatusOptions" placeholder="All" :allow-empty="true"
                    class="text-sm" />
            </div>
            <div class="flex gap-3 ml-auto">
                <button
                    @click="applyFilters"
                    class="bg-positive text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                    Apply Filters
                </button>
                <button
                    @click="resetFilters"
                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                    Reset Filters
                </button>
            </div>
        </div>
           </div>
      <!-- Table -->
        <!-- Data Table -->
        <div class="overflow-x-auto">
            <table class="w-full text-sm border border-gray-200 rounded-lg">
                <thead class="bg-primary/50">
                    <tr class="text-left text-tertiary font-semibold">
                        <th class="p-3">Subclass</th>
                        <th class="p-3">Store ID</th>
                        <th class="p-3">Month</th>
                        <th class="p-3">LM</th>
                        <th class="p-3">LM Contribution</th>
                        <th class="p-3">GMV/day</th>
                        <th class="p-3">Outlier Status</th>
                        <th class="p-3">Suggested Value</th>
                        <th class="p-3">Action</th>
                    </tr>
                </thead>
               <tbody>
          <tr v-for="(row, index) in rows" :key="index">
            <td class="py-2 px-4">{{ row.subclass }}</td>
            <td class="py-2 px-4">{{ row.storeId }}</td>
            <td class="py-2 px-4">{{ row.month }}</td>
            <td class="py-2 px-4">{{ row.totalLm }}</td>
            <td class="py-2 px-4">{{ row.lmContrib }}</td>
            <td class="py-2 px-4">{{ row.gmvPerDay }}</td>
            <td class="py-2 px-4">
              <span :class="getOutlierStatusClass(row.outlierStatus)">
                {{ row.outlierStatus }}
              </span>
            </td>
            <td class="py-2 px-4">{{ row.suggestedTotalLm }}</td>
            <td class="py-2 px-4">
              <button
                v-if="isOutlier(row.outlierStatus)"
                class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs"
                @click="openChat(row)"
              >
                {{ row.action }}
              </button>
            </td>
          </tr>
        </tbody>
            </table>
        </div>

    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted,watch } from 'vue'
import Multiselect from '@vueform/multiselect' // corrected import
import '@vueform/multiselect/themes/default.css' // required for styling
import outlierChat from './outlierChat.vue';
import axios from 'axios'

// Filters and options
const filters = ref({
    storeId: [],
    subclass: [],
    outlierStatus: [] // Default selected
});

// Static options based on all available data
const subclassOptions = computed(() => {
    const allSubclasses = [...new Set(originalRows.value.map(r => r.subclass))];
    return allSubclasses.map(s => ({ value: s, label: s }));
});

const storeOptions = computed(() => {
    const allStores = [...new Set(originalRows.value.map(r => r.storeId))];
    return allStores.map(id => ({ value: id, label: id }));
});

const outlierStatusOptions = [
  { value: 'MAJOR_OUTLIER', label: 'Major Outlier' },
  { value: 'MINOR_OUTLIER', label: 'Minor Outlier' },
  { value: 'NORMAL', label: 'Normal' },
];

// Data
const rows = ref([]);
const originalRows = ref([]); // Keep original data separate
const loading = ref(false);
const error = ref(null);

const fetchOutlierData = async () => {
    loading.value = true;
    error.value = null;
    
    try {
        const response = await axios.get('scenario/outlier/');
        console.log("response.data", response.data);

        const mappedData = response.data.map(item => ({
            subclass: item.sub_clss_nm,
            storeId: item.loc_cd,
            month: item.month,
            totalLm: item.total_lm,
            lmContrib: `${item.lm_contribution_in_store}%`,
            gmvPerDay: item.gmv_per_day,
            outlierStatus: item.outlier_status || 'NORMAL',
            suggestedTotalLm: item.suggested_total_lm,
            action: 'Approve' // Default action
        }));

        // Store original data
        originalRows.value = mappedData;

        // Set default filters
        filters.value.outlierStatus = ['MAJOR_OUTLIER', 'MINOR_OUTLIER'];

        // Apply initial filters
        applyFilters();

    } catch (err) {
        console.error('Error fetching outlier data:', err);
        error.value = err.response?.data?.message || err.message || 'Failed to load outlier data';
    } finally {
        loading.value = false;
    }
}

const isOutlier = (status) => ["MAJOR_OUTLIER", "MINOR_OUTLIER"].includes(status);

const getOutlierStatusClass = (status) => {
    switch (status) {
        case "NORMAL": return "bg-positive text-white px-2 py-1 rounded text-xs";
        case "MINOR_OUTLIER": return "bg-neutral text-white px-2 py-1 rounded text-xs";
        case "MAJOR_OUTLIER": return "bg-negative text-white px-2 py-1 rounded text-xs";
        default: return "bg-gray-500 text-white px-2 py-1 rounded text-xs";
    }
}

// Filter functions
const applyFilters = () => {
    console.log('Applying filters:', filters.value);

    // Always filter from original data to avoid destructive filtering
    let filtered = [...originalRows.value];

    // Subclass filter - empty means "show all"
    if (filters.value.subclass.length > 0) {
        filtered = filtered.filter(row => filters.value.subclass.includes(row.subclass));
    }

    // Store filter - empty means "show all"
    if (filters.value.storeId.length > 0) {
        filtered = filtered.filter(row => filters.value.storeId.includes(row.storeId));
    }


    if (filters.value.outlierStatus.length > 0) {
        filtered = filtered.filter(row => filters.value.storeId.includes(row.outlierStatus));
    }    

    rows.value = filtered;

    console.log(`Filtered ${filtered.length} rows from ${originalRows.value.length} total`);
}

const resetFilters = () => {
    filters.value = {
        storeId: [],
        subclass: [],
        outlierStatus: [] // Reset to default
    };
    applyFilters(); // Apply filters after reset (don't refetch data)
    console.log('Filters reset to defaults');
}

// Helper functions for "Select All" and "Clear" functionality
const selectAllSubclasses = () => {
    filters.value.subclass = subclassOptions.value.map(opt => opt.value);
}

const clearSubclasses = () => {
    filters.value.subclass = [];
}

const selectAllStores = () => {
    filters.value.storeId = storeOptions.value.map(opt => opt.value);
}

const clearStores = () => {
    filters.value.storeId = [];
}

const selectAllOutliers = () => {
    filters.value.outlierStatus = outlierStatusOptions.map(opt => opt.value);
}

const clearOutliers = () => {
    filters.value.outlierStatus = [];
}



// Optional: Auto-apply filters on change (remove if you want manual Apply only)
watch(filters, () => {
    applyFilters();
}, { deep: true });

onMounted(() => {
    fetchOutlierData();
});
</script>
